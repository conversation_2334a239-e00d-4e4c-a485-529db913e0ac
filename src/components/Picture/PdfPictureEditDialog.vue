<template>
  <div class="mask" />
  <div class="pdf-dialog">
    <div class="info flex">
      <div class="left flex chessboard-bg">
        <img
          :style="{
            width: width + 'px',
            height: height + 'px',
            opacity: currentPicture.opacity / 100,
            transform: `rotate(${currentPicture.rotate}deg) scaleX(${currentPicture.scaleX}) scaleY(${currentPicture.scaleY})`,
            zIndex: 1,
          }"
          :src="
            url ||
            currentPicture.record.imgUrl ||
            '/img/pic/index/<EMAIL>'
          "
          alt=""
        />
      </div>
      <div class="right">
        <img
          class="close"
          @click="closeDialog(currentPicture)"
          src="/img/mind_map/table/<EMAIL>"
          alt=""
        />
        <p class="title">图片编辑</p>
        <div class="action rotate flex">
          <div class="tip">旋转</div>
          <el-slider v-model="currentPicture.rotate" :max="360" />
          <div class="size">{{ currentPicture.rotate }}°</div>
        </div>
        <div class="action opacity flex">
          <div class="tip">透明度</div>
          <el-slider v-model="currentPicture.opacity" :max="100" />
          <div class="size">{{ currentPicture.opacity }}%</div>
        </div>
        <div class="action btns flex">
          <div
            class="item flex"
            @click="currentPicture.scaleX = -currentPicture.scaleX"
          >
            <img src="/img/pic/second/<EMAIL>" alt="" />
            <div class="tip">左右翻转</div>
          </div>
          <div
            class="item flex"
            @click="currentPicture.scaleY = -currentPicture.scaleY"
          >
            <img src="/img/pic/second/<EMAIL>" alt="" />
            <div class="tip">上下翻转</div>
          </div>
          <div
            class="item flex"
            @click="
              () => {
                currentPicture.rotate = 0
                currentPicture.scaleX = 1
                currentPicture.scaleY = 1
              }
            "
          >
            <img src="/img/pic/second/<EMAIL>" alt="" />
            <div class="tip">重置</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onUnmounted } from 'vue'
import { storeToRefs } from 'pinia'
import { usePdfStore } from '@/pinia/pdf'
import {
  getTransformImage,
  // getLimitWH,
} from '@/utils/image'

const pdfStore = usePdfStore()
let { showPdfPictureEdit, currentPicture } = storeToRefs(pdfStore)

const rate = currentPicture.value.width / currentPicture.value.height
const canvasWidth = 592
const canvasHeight = 646
let width = 0
let height = 0
if (rate > 1) {
  width = canvasWidth
  height = canvasWidth / rate
} else {
  width = canvasHeight * rate
  height = canvasHeight
}

async function closeDialog(map: any) {
  currentPicture.value.record.loading = true
  showPdfPictureEdit.value = false
  await new Promise((resolve) => setTimeout(() => resolve(true), 10)) // 大图使用loading吗？

  map.base64 = getTransformImage({
    rotate: map.rotate,
    scaleX: map.scaleX,
    scaleY: map.scaleY,
    opacity: map.opacity,
    limitWidth: 4096, // pdf限宽4k
    format: currentPicture.value.record.newFormat,
    currentImage: map.image,
  })
  currentPicture.value.record.loading = false
}

const url = URL.createObjectURL(currentPicture.value.file)
onUnmounted(() => {
  URL.revokeObjectURL(url)
})
</script>

<style lang="scss" scoped>
.pdf-dialog {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 984px;
  border-radius: 4px;
  z-index: 2001;

  :deep(.el-slider) {
    height: 20px;
    width: 251px;

    .el-slider__runway {
      height: 3px;
      border-radius: 3px;
      background-color: #e4e4e6;

      .el-slider__bar {
        height: 3px !important;
        border-radius: 3px;
      }

      .el-slider__button {
        position: relative;
        width: 9px;
        height: 9px;
        top: -2px;
        border: #1890ff;
        background: #1890ff;
      }
    }
  }

  .info {
    position: fixed;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    text-align: center;

    width: 984px;
    height: 646px;
    border-radius: 4px;
    background: #fff;
    @include common-dialog;

    .left {
      width: 592px;
      height: 646px;
      // background: #f7f7f7;

      justify-content: center;
      align-items: center;
      overflow: hidden;

      // img {
      //   max-width: 100%;
      //   max-height: 100%;
      //   object-fit: contain;
      // }
    }
    .right {
      position: relative;
      width: 392px;
      height: 646px;
      padding: 42px 28px;
      text-align: left;
      background: #ffffff;

      .close {
        position: absolute;
        right: 14px;
        top: 14px;
        margin-bottom: 25px;
        height: 20px;
        width: 20px;
        cursor: pointer;

        &:hover {
          opacity: 0.7;
        }
      }

      .title {
        font-weight: 600; // electron
        font-size: 14px;
        color: #333333;
        line-height: 20px;
      }

      .action {
        min-width: 336px;
        height: 20px;
        margin-top: 20px;
        user-select: none;

        .tip {
          width: 54px;
          height: 20px;
          flex-shrink: 0;

          font-weight: 400;
          font-size: 14px;
          color: #999999;
          line-height: 20px;
        }

        .size {
          width: 20px;
          margin-left: 12px;
          font-weight: 400;
          font-size: 12px;
          color: #333333;
          line-height: 17px;
          text-align: left;
          flex-shrink: 0;
        }
      }

      .btns {
        margin-top: 28px;

        .item {
          margin-right: 32px;
          cursor: pointer;

          img {
            width: 24px;
            height: 24px;
          }

          .tip {
            width: auto;
            margin-left: 4px;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            line-height: 24px;
          }
        }
      }
    }
  }
}
</style>
