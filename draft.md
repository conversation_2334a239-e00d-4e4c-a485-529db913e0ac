==== 重构：手动精修、换背景、改尺寸 ====
需求：Matting/index.vue 在不影响现有功能的前提下（注意保留原来注释掉的代码），将手动精修、换背景代码提取到单独的组件中，可以参考 <ResizeBox></ResizeBox> 的提取，是我想要的
说明：
1、关于样式：.chessboard-bg是全局样式，不需要重新定义提取，.box可以复制一份过去，将index.scss中相关组件的样式也提取过去（并移除已提取的样式），注意不要添加新的样式
2、batchStore 通过组件的currentStore属性传递过去，不要重新定义变量，组件中的属性，优先使用currentStore里面的
3、如果是多个组件共用的方法、变量，可以提取到 matting.ts中
4、注意不要改坏原来功能，不改变原代码逻辑，不改变原dom结构(如：info-box是manual-refine-box里面的，不要改变dom结构)，已提取的代码删除掉
5、重构完之后，从未重构前的代码重新分析下已经修改的代码，确保功能不要遗失，以原代码原逻辑为准，已经不一样的逻辑去改成一样，确保跟原来保持一致，原来功能都是好的

==== 重构：动态getStore ====
1、FootTool.vue、BackgroundBox.vue、ManualRefineBox.vue、ResizeBox.vue 4个组件中是否都使用 getStore？

========================================================


需求：选中排版照时，在class="multi"里面实现图片中的内容，单张照、排版照tab已经做好需要补充其他内容

证件照图片：currentImg.mattingCropedImage

具体要求：
1、5寸-A4为排版的尺寸，需要进行缩放(缩放到最大宽为：620px，最大高为：450px)假定变量为multiScale，让证件照图片可以进行排版，默认是2行5列进行平铺，支持手动设置行列数
2、图片中的（152*102mm），152mm为宽，102mm为高，图片中（89*127mm）宽高写反了注意下
3、证件照图片需要按原宽高比进行缩放，以适配可以放入2行5列排版中或其他行列排版中
4、图片的绘制使用的是canvas，排版里面的标线注意要保留，排版外面的尺寸线也要保留，具体算法可以参考单张照里面的


需要根据图片的真实缩放后宽高：naturalWidth*multiScale、naturalHeight*multiScale，判断能否在排版照中放的下
-- 如果能放下，根据图片的真实缩放后宽高计算行列（横向放不下，需要判断纵向是否放得下可以纵向放）
-- 如果放不下，弹框提示用户：当前照片尺寸>排版照尺寸，无法置于**相纸生成排版照，请重新设置照片尺寸/排版尺寸

1、相纸尺寸竖向平铺在界面，白底黑字
2、选择自定义时，可以设置行数、列数，如第一张图
3、选择其他时，显示其他尺寸显示列表，如第二张图
4、multi-canvas 标尺没有显示，只显示mm不显示px请参考单张照里面的ruler-horizontal、ruler-vertical
5、检查证件照是否能放入单个格子，这个不需要，直接将原图按比例缩放，以适配2行5列，
如果是其他行列，也是按原图比例缩放以适配当前列数行数


1、el-select 还是不行，请参考ToolText.vue里面的el-select进行处理，使下拉框的选项可以显示
2、排版照中的图片分隔线，应该是根据单张照的比例，进行实现描边，描边的线条色为：#EEEEEE，排版照中的每张图片，上下左右间距各设置5px（写个注释提醒我下，可能需要调整）
3、导出时，
如果是单张照，导出的宽高需要按mm进行还原，如：89mm宽导出时宽应该为Math.round(89*11.81)px，高同理
如果是排版照，如：152mm宽导出时宽应该为Math.round(152*11.81)px，高同理


1、el-select 选择内容不显示，是因为弹框蒙层层级较z-index高达到了3001,所以需要修改下el-popper的层级，至少3002!important
2、导出时，
如果是单张照，导出的宽高需要按mm进行还原，如：89mm宽导出时宽应该为Math.round(89*11.81)px，高同理
如果是排版照，如：152mm宽导出时宽应该为Math.round(152*11.81)px，高同理